#!/usr/bin/env python3
"""
Test script to verify that all dependencies are properly installed
and the environment is ready for YOLOv8 to TensorFlow Lite conversion.
"""

import sys
import os

def test_imports():
    """Test if all required packages can be imported."""
    
    print("Testing package imports...")
    
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import torchvision
        print(f"✓ TorchVision: {torchvision.__version__}")
    except ImportError as e:
        print(f"❌ TorchVision import failed: {e}")
        return False
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow: {tf.__version__}")
    except ImportError as e:
        print(f"❌ TensorFlow import failed: {e}")
        return False
    
    try:
        from ultralytics import YOLO
        print(f"✓ Ultralytics YOLOv8 imported successfully")
    except ImportError as e:
        print(f"❌ Ultralytics import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    return True

def test_model_file():
    """Test if the model file exists."""
    
    print("\nTesting model file...")
    
    model_path = "best.pt"
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        print(f"✓ Model file found: {model_path} ({file_size:.2f} MB)")
        return True
    else:
        print(f"❌ Model file not found: {model_path}")
        print("   Make sure your trained YOLOv8 model is named 'best.pt' and in the current directory")
        return False

def test_tensorflow_lite():
    """Test TensorFlow Lite functionality."""
    
    print("\nTesting TensorFlow Lite...")
    
    try:
        import tensorflow as tf
        
        # Test TFLite interpreter creation
        # Create a simple model to test
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(1, input_shape=(1,))
        ])
        
        # Convert to TFLite
        converter = tf.lite.TFLiteConverter.from_keras_model(model)
        tflite_model = converter.convert()
        
        # Test interpreter
        interpreter = tf.lite.Interpreter(model_content=tflite_model)
        interpreter.allocate_tensors()
        
        print("✓ TensorFlow Lite functionality working")
        return True
        
    except Exception as e:
        print(f"❌ TensorFlow Lite test failed: {e}")
        return False

def main():
    """Run all tests."""
    
    print("=" * 60)
    print("YOLOv8 to TensorFlow Lite - Environment Test")
    print("=" * 60)
    
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print()
    
    # Run tests
    imports_ok = test_imports()
    model_ok = test_model_file()
    tflite_ok = test_tensorflow_lite()
    
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    
    if imports_ok and model_ok and tflite_ok:
        print("🎉 All tests passed! Your environment is ready for conversion.")
        print("\nYou can now run the conversion with:")
        print("   python main.py")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        
        if not imports_ok:
            print("\n📦 To fix import issues, try:")
            print("   pip install -r requirements.txt")
        
        if not model_ok:
            print("\n📁 To fix model file issues:")
            print("   - Ensure your YOLOv8 model is named 'best.pt'")
            print("   - Place it in the same directory as this script")
        
        if not tflite_ok:
            print("\n🔧 To fix TensorFlow Lite issues:")
            print("   - Try reinstalling TensorFlow: pip install --upgrade tensorflow")
            print("   - On Windows, ensure Visual C++ redistributables are installed")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
