#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/diagflat_ops.h>

namespace at {


// aten::diagflat(Tensor self, int offset=0) -> Tensor
inline at::Tensor diagflat(const at::Tensor & self, int64_t offset=0) {
    return at::_ops::diagflat::call(self, offset);
}

}
