# YOLOv8 to TensorFlow Lite Conversion Guide

This project converts your custom YOLOv8s model (`best.pt`) to TensorFlow Lite format for mobile and edge deployment.

## Setup Instructions

### 1. Activate Virtual Environment
```bash
# Activate the virtual environment
.venv\Scripts\activate
```

### 2. Install Dependencies
```bash
# Install all required packages
pip install -r requirements.txt
```

## Usage

### Basic Conversion
Convert your `best.pt` model with default settings:
```bash
python main.py
```

### Advanced Options
```bash
# Custom model path and output directory
python main.py --model path/to/your/model.pt --output ./my_converted_models

# Different input size (e.g., 416x416)
python main.py --imgsz 416

# Disable quantization (larger file, potentially better accuracy)
python main.py --no-quantize

# Enable INT8 quantization (smaller file, requires representative dataset)
python main.py --int8

# Enable dynamic input shapes
python main.py --dynamic

# Combine multiple options
python main.py --model best.pt --imgsz 320 --int8 --output ./mobile_models
```

### Command Line Arguments

| Argument | Short | Default | Description |
|----------|-------|---------|-------------|
| `--model` | `-m` | `best.pt` | Path to YOLOv8 .pt model file |
| `--output` | `-o` | `./converted_models` | Output directory for converted models |
| `--imgsz` | `-s` | `640` | Input image size |
| `--no-quantize` | | `False` | Disable dynamic range quantization |
| `--int8` | | `False` | Enable INT8 quantization |
| `--dynamic` | | `False` | Enable dynamic input shapes |

## Output

The script will:
1. ✅ Verify your input model file exists
2. 🔄 Load the YOLOv8 model
3. 🚀 Export to TensorFlow Lite format
4. 📁 Save the converted model with a descriptive filename
5. ✅ Verify the converted model and show details

### Output Files
- **Quantized model**: `yolov8s_quantized_640.tflite`
- **Non-quantized model**: `yolov8s_fp32_640.tflite`

## Model Optimization Options

### Dynamic Range Quantization (Default)
- **Pros**: Smaller file size, faster inference
- **Cons**: Slight accuracy loss
- **Use case**: Most mobile applications

### INT8 Quantization
- **Pros**: Smallest file size, fastest inference
- **Cons**: Requires representative dataset, more accuracy loss
- **Use case**: Resource-constrained devices

### No Quantization (FP32)
- **Pros**: Best accuracy
- **Cons**: Larger file size, slower inference
- **Use case**: When accuracy is critical and resources allow

## Troubleshooting

### Common Issues

1. **Model file not found**
   - Ensure `best.pt` is in the current directory
   - Use `--model` to specify the correct path

2. **TensorFlow installation issues**
   - Try installing TensorFlow CPU version: `pip install tensorflow-cpu`
   - On Windows, ensure Visual C++ redistributables are installed

3. **CUDA/GPU issues**
   - The conversion process primarily uses CPU
   - GPU is not required for model conversion

4. **Memory issues**
   - Close other applications
   - Try with a smaller input size using `--imgsz`

### Verification Output
The script will show detailed information about the converted model:
- File size in MB
- Input tensor details (shape, type)
- Output tensor details (shape, type)

## Next Steps

After successful conversion, you can:
1. **Test the model** with TensorFlow Lite runtime
2. **Integrate into mobile apps** (Android/iOS)
3. **Deploy to edge devices** (Raspberry Pi, etc.)
4. **Optimize further** with additional TensorFlow Lite tools

## Performance Tips

- **Input size**: Smaller sizes (320, 416) = faster inference, larger sizes (640, 800) = better accuracy
- **Quantization**: Enable for mobile deployment unless accuracy is critical
- **Dynamic shapes**: Only enable if you need variable input sizes (adds overhead)

## File Structure After Conversion
```
litert-test/
├── best.pt                    # Your original model
├── main.py                    # Conversion script
├── requirements.txt           # Dependencies
├── converted_models/          # Output directory
│   └── yolov8s_quantized_640.tflite
└── .venv/                     # Virtual environment
```
