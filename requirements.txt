# Core YOLOv8 and model conversion dependencies
ultralytics>=8.0.0

# TensorFlow and TensorFlow Lite dependencies
tensorflow>=2.13.0
tensorflow-lite>=2.13.0

# PyTorch (required for loading .pt models)
torch>=2.0.0
torchvision>=0.15.0

# Additional dependencies for model conversion
onnx>=1.14.0
onnxruntime>=1.15.0

# Image processing and utilities
opencv-python>=4.8.0
Pillow>=9.5.0
numpy>=1.24.0

# Optional: For quantization and optimization
tensorflow-model-optimization>=0.7.0

# Utilities
tqdm>=4.65.0
pyyaml>=6.0
