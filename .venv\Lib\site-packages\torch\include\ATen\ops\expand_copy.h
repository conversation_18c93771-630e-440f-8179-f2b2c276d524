#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/expand_copy_ops.h>

namespace at {


// aten::expand_copy(Tensor self, SymInt[] size, *, bool implicit=False) -> Tensor
inline at::Tensor expand_copy(const at::Tensor & self, at::IntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy::call(self, c10::fromIntArrayRefSlow(size), implicit);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor expand_copy(const at::Tensor & self, at::IntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy::call(self, c10::fromIntArrayRefSlow(size), implicit);
  }
}

// aten::expand_copy(Tensor self, SymInt[] size, *, bool implicit=False) -> Tensor
inline at::Tensor expand_copy_symint(const at::Tensor & self, c10::SymIntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy::call(self, size, implicit);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor expand_copy(const at::Tensor & self, c10::SymIntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy::call(self, size, implicit);
  }
}

// aten::expand_copy.out(Tensor self, SymInt[] size, *, bool implicit=False, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & expand_copy_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy_out::call(self, c10::fromIntArrayRefSlow(size), implicit, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & expand_copy_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy_out::call(self, c10::fromIntArrayRefSlow(size), implicit, out);
  }
}

// aten::expand_copy.out(Tensor self, SymInt[] size, *, bool implicit=False, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & expand_copy_outf(const at::Tensor & self, at::IntArrayRef size, bool implicit, at::Tensor & out) {
    return at::_ops::expand_copy_out::call(self, c10::fromIntArrayRefSlow(size), implicit, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor & expand_copy_outf(const at::Tensor & self, at::IntArrayRef size, bool implicit, at::Tensor & out) {
    return at::_ops::expand_copy_out::call(self, c10::fromIntArrayRefSlow(size), implicit, out);
  }
}

// aten::expand_copy.out(Tensor self, SymInt[] size, *, bool implicit=False, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & expand_copy_symint_out(at::Tensor & out, const at::Tensor & self, c10::SymIntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy_out::call(self, size, implicit, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & expand_copy_out(at::Tensor & out, const at::Tensor & self, c10::SymIntArrayRef size, bool implicit=false) {
    return at::_ops::expand_copy_out::call(self, size, implicit, out);
  }
}

// aten::expand_copy.out(Tensor self, SymInt[] size, *, bool implicit=False, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & expand_copy_symint_outf(const at::Tensor & self, c10::SymIntArrayRef size, bool implicit, at::Tensor & out) {
    return at::_ops::expand_copy_out::call(self, size, implicit, out);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor & expand_copy_outf(const at::Tensor & self, c10::SymIntArrayRef size, bool implicit, at::Tensor & out) {
    return at::_ops::expand_copy_out::call(self, size, implicit, out);
  }
}

}
