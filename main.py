#!/usr/bin/env python3
"""
YOLOv8 to TensorFlow Lite Converter

This script converts a YOLOv8 PyTorch model (.pt) to TensorFlow Lite (.tflite) format
optimized for mobile and edge deployment scenarios.
"""

import os
import sys
from pathlib import Path
import argparse
from ultralytics import YOLO
import tensorflow as tf


def check_model_file(model_path):
    """Check if the model file exists and is valid."""
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model file not found: {model_path}")

    if not model_path.endswith(".pt"):
        raise ValueError(f"Expected .pt file, got: {model_path}")

    print(f"✓ Found model file: {model_path}")
    return True


def convert_yolo_to_tflite(
    model_path="best.pt",
    output_dir="./converted_models",
    imgsz=640,
    quantize=True,
    int8=False,
    dynamic=False,
):
    """
    Convert YOLOv8 model to TensorFlow Lite format.

    Args:
        model_path (str): Path to the YOLOv8 .pt model file
        output_dir (str): Directory to save converted models
        imgsz (int): Input image size for the model
        quantize (bool): Apply dynamic range quantization
        int8 (bool): Apply INT8 quantization (requires representative dataset)
        dynamic (bool): Enable dynamic input shapes

    Returns:
        str: Path to the converted .tflite file
    """

    # Check if model file exists
    check_model_file(model_path)

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    print(f"Loading YOLOv8 model from: {model_path}")

    try:
        # Load the YOLOv8 model
        model = YOLO(model_path)

        # Print model information
        print(f"✓ Model loaded successfully")
        print(f"  - Model type: {type(model.model).__name__}")
        print(f"  - Input size: {imgsz}")

        # Export to TensorFlow Lite
        print(f"\nStarting TensorFlow Lite export...")
        print(f"  - Output directory: {output_dir}")
        print(f"  - Quantization: {'Enabled' if quantize else 'Disabled'}")
        print(f"  - INT8 quantization: {'Enabled' if int8 else 'Disabled'}")
        print(f"  - Dynamic shapes: {'Enabled' if dynamic else 'Disabled'}")

        # Configure export parameters
        export_kwargs = {
            "format": "tflite",
            "imgsz": imgsz,
            "dynamic": dynamic,
            "int8": int8,
        }

        # Perform the export
        exported_model = model.export(**export_kwargs)

        # Move the exported file to our desired output directory
        original_path = Path(exported_model)
        new_filename = (
            f"yolov8s_{'quantized' if quantize or int8 else 'fp32'}_{imgsz}.tflite"
        )
        new_path = Path(output_dir) / new_filename

        # Copy the file to the new location
        import shutil

        shutil.copy2(original_path, new_path)

        print(f"✓ Export completed successfully!")
        print(f"  - Original file: {original_path}")
        print(f"  - Final location: {new_path}")

        return str(new_path)

    except Exception as e:
        print(f"❌ Error during conversion: {str(e)}")
        raise


def verify_tflite_model(tflite_path):
    """
    Verify the converted TensorFlow Lite model.

    Args:
        tflite_path (str): Path to the .tflite file
    """

    if not os.path.exists(tflite_path):
        print(f"❌ TFLite file not found: {tflite_path}")
        return False

    try:
        # Load the TFLite model
        interpreter = tf.lite.Interpreter(model_path=tflite_path)
        interpreter.allocate_tensors()

        # Get input and output details
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()

        # Get file size
        file_size = os.path.getsize(tflite_path) / (1024 * 1024)  # MB

        print(f"\n✓ TensorFlow Lite model verification:")
        print(f"  - File: {tflite_path}")
        print(f"  - File size: {file_size:.2f} MB")
        print(f"  - Input tensors: {len(input_details)}")
        print(f"  - Output tensors: {len(output_details)}")

        # Print input details
        for i, input_detail in enumerate(input_details):
            print(f"  - Input {i}: {input_detail['name']}")
            print(f"    - Shape: {input_detail['shape']}")
            print(f"    - Type: {input_detail['dtype']}")

        # Print output details
        for i, output_detail in enumerate(output_details):
            print(f"  - Output {i}: {output_detail['name']}")
            print(f"    - Shape: {output_detail['shape']}")
            print(f"    - Type: {output_detail['dtype']}")

        return True

    except Exception as e:
        print(f"❌ Error verifying TFLite model: {str(e)}")
        return False


def main():
    """Main function to handle command line arguments and run conversion."""

    parser = argparse.ArgumentParser(
        description="Convert YOLOv8 PyTorch model to TensorFlow Lite format"
    )

    parser.add_argument(
        "--model",
        "-m",
        type=str,
        default="best.pt",
        help="Path to YOLOv8 .pt model file (default: best.pt)",
    )

    parser.add_argument(
        "--output",
        "-o",
        type=str,
        default="./converted_models",
        help="Output directory for converted models (default: ./converted_models)",
    )

    parser.add_argument(
        "--imgsz", "-s", type=int, default=640, help="Input image size (default: 640)"
    )

    parser.add_argument(
        "--no-quantize", action="store_true", help="Disable dynamic range quantization"
    )

    parser.add_argument(
        "--int8",
        action="store_true",
        help="Enable INT8 quantization (requires representative dataset)",
    )

    parser.add_argument(
        "--dynamic", action="store_true", help="Enable dynamic input shapes"
    )

    args = parser.parse_args()

    print("=" * 60)
    print("YOLOv8 to TensorFlow Lite Converter")
    print("=" * 60)

    try:
        # Convert the model
        tflite_path = convert_yolo_to_tflite(
            model_path=args.model,
            output_dir=args.output,
            imgsz=args.imgsz,
            quantize=not args.no_quantize,
            int8=args.int8,
            dynamic=args.dynamic,
        )

        # Verify the converted model
        if verify_tflite_model(tflite_path):
            print(f"\n🎉 Conversion completed successfully!")
            print(f"Your TensorFlow Lite model is ready at: {tflite_path}")
            print(f"\nThis model is optimized for mobile and edge deployment.")
        else:
            print(f"\n⚠️  Conversion completed but verification failed.")

    except Exception as e:
        print(f"\n❌ Conversion failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
